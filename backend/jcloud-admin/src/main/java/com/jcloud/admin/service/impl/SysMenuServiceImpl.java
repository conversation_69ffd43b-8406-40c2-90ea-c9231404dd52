package com.jcloud.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.jcloud.admin.service.SysMenuService;
import com.jcloud.admin.service.SysUserService;
import com.jcloud.admin.service.SysRoleMenuService;
import com.jcloud.common.constant.CommonConstants;
import com.jcloud.common.dto.MenuCreateRequest;
import com.jcloud.common.dto.MenuQueryRequest;
import com.jcloud.common.dto.MenuUpdateRequest;
import com.jcloud.common.entity.SysMenu;
import com.jcloud.common.entity.SysUser;
import com.jcloud.common.exception.BusinessException;
import com.jcloud.common.mapper.SysMenuMapper;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.result.ResultCode;
import com.jcloud.common.service.impl.BaseServiceImpl;
import com.jcloud.common.util.SecurityUtils;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜单服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysMenuServiceImpl extends BaseServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {

    private final SysUserService userService;
    private final SysRoleMenuService roleMenuService;
    
    @Override
    public PageResult<SysMenu> pageMenus(MenuQueryRequest queryRequest) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        
        // 菜单表不需要租户过滤，因为菜单是全局的
        queryWrapper.eq("deleted", CommonConstants.NOT_DELETED);
        
        // 构建查询条件
        if (queryRequest.getParentId() != null) {
            queryWrapper.eq("parent_id", queryRequest.getParentId());
        }
        if (StrUtil.isNotBlank(queryRequest.getMenuName())) {
            queryWrapper.like("menu_name", queryRequest.getMenuName());
        }
        if (queryRequest.getMenuType() != null) {
            queryWrapper.eq("menu_type", queryRequest.getMenuType());
        }
        if (StrUtil.isNotBlank(queryRequest.getPath())) {
            queryWrapper.like("path", queryRequest.getPath());
        }
        if (StrUtil.isNotBlank(queryRequest.getPermissionCode())) {
            queryWrapper.like("permission_code", queryRequest.getPermissionCode());
        }
        if (queryRequest.getStatus() != null) {
            queryWrapper.eq("status", queryRequest.getStatus());
        }
        if (queryRequest.getVisible() != null) {
            queryWrapper.eq("visible", queryRequest.getVisible());
        }
        if (queryRequest.getCreateTimeStart() != null) {
            queryWrapper.ge("create_time", queryRequest.getCreateTimeStart());
        }
        if (queryRequest.getCreateTimeEnd() != null) {
            queryWrapper.le("create_time", queryRequest.getCreateTimeEnd());
        }
        
        // 排序
        queryWrapper.orderBy("sort_order", true)
                   .orderBy("create_time", false);
        
        // 分页查询
        Page<SysMenu> page = Page.of(queryRequest.getPageNum(), queryRequest.getPageSize());
        Page<SysMenu> pageResult = baseMapper.paginate(page, queryWrapper);
        
        return PageResult.of(pageResult.getRecords(), pageResult.getTotalRow(), 
                           queryRequest.getPageNum(), queryRequest.getPageSize());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createMenu(MenuCreateRequest createRequest) {
        // 检查父菜单是否存在
        if (createRequest.getParentId() != 0) {
            SysMenu parentMenu = getById(createRequest.getParentId());
            if (parentMenu == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "父菜单不存在");
            }
        }
        
        // 检查菜单名称是否存在（同级菜单下）
        if (isMenuNameExists(createRequest.getMenuName(), createRequest.getParentId(), null)) {
            throw new BusinessException(ResultCode.CONFLICT, "同级菜单下菜单名称已存在");
        }
        
        // 检查路由路径是否存在
        if (StrUtil.isNotBlank(createRequest.getPath()) && 
            isPathExists(createRequest.getPath(), null)) {
            throw new BusinessException(ResultCode.CONFLICT, "路由路径已存在");
        }
        
        // 创建菜单
        SysMenu menu = new SysMenu();
        BeanUtils.copyProperties(createRequest, menu);
        setBaseInfo(menu, false);
        
        boolean success = save(menu);
        if (!success) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "创建菜单失败");
        }
        
        log.info("创建菜单成功，菜单名称：{}，菜单类型：{}", menu.getMenuName(), menu.getMenuType());
        return true;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMenu(MenuUpdateRequest updateRequest) {
        SysMenu existingMenu = getById(updateRequest.getId());
        if (existingMenu == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "菜单不存在");
        }
        
        // 检查父菜单是否存在（如果要更改父菜单）
        if (updateRequest.getParentId() != null && updateRequest.getParentId() != 0) {
            SysMenu parentMenu = getById(updateRequest.getParentId());
            if (parentMenu == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "父菜单不存在");
            }
            
            // 检查是否形成循环引用
            if (isCircularReference(updateRequest.getId(), updateRequest.getParentId())) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "不能将菜单移动到自己的子菜单下");
            }
        }
        
        // 检查菜单名称是否存在（同级菜单下，排除当前菜单）
        Long parentId = updateRequest.getParentId() != null ? updateRequest.getParentId() : existingMenu.getParentId();
        if (StrUtil.isNotBlank(updateRequest.getMenuName()) && 
            isMenuNameExists(updateRequest.getMenuName(), parentId, updateRequest.getId())) {
            throw new BusinessException(ResultCode.CONFLICT, "同级菜单下菜单名称已存在");
        }
        
        // 检查路由路径是否存在（排除当前菜单）
        if (StrUtil.isNotBlank(updateRequest.getPath()) && 
            isPathExists(updateRequest.getPath(), updateRequest.getId())) {
            throw new BusinessException(ResultCode.CONFLICT, "路由路径已存在");
        }
        
        // 更新菜单信息
        SysMenu menu = new SysMenu();
        BeanUtils.copyProperties(updateRequest, menu);
        setBaseInfo(menu, true);
        
        boolean success = updateById(menu);
        if (!success) {
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新菜单失败");
        }
        
        log.info("更新菜单成功，菜单ID：{}，菜单名称：{}", menu.getId(), menu.getMenuName());
        return true;
    }
    
    @Override
    public List<SysMenu> getMenusByParentId(Long parentId) {
        return baseMapper.selectByParentId(parentId);
    }
    
    @Override
    public List<SysMenu> getMenusByUserId(Long userId) {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "无法获取租户信息");
        }

        // 统一使用角色-菜单关联逻辑获取菜单
        // 无论是超级管理员还是普通用户，都通过角色分配的菜单来获取
        List<SysMenu> userMenus = roleMenuService.getMenusByUserId(userId);
        if (userMenus.isEmpty()) {
            log.warn("用户没有分配任何菜单权限: userId={}, tenantId={}", userId, tenantId);
            return new ArrayList<>();
        }

        log.info("用户菜单查询成功: userId={}, menuCount={}", userId, userMenus.size());
        return userMenus;
    }
    
    @Override
    public List<SysMenu> getMenusByType(Integer menuType) {
        return baseMapper.selectByMenuType(menuType);
    }
    
    @Override
    public SysMenu getMenuByPermissionCode(String permissionCode) {
        return baseMapper.selectByPermissionCode(permissionCode);
    }
    
    @Override
    public boolean isMenuNameExists(String menuName, Long parentId, Long excludeMenuId) {
        Long tenantId = SecurityUtils.getTenantId();
        return baseMapper.existsByMenuNameAndParentId(menuName, parentId, excludeMenuId, tenantId) > 0 ;
    }

    @Override
    public boolean isPathExists(String path, Long excludeMenuId) {
        Long tenantId = SecurityUtils.getTenantId();
        return baseMapper.existsByPath(path, excludeMenuId, tenantId) > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMenuStatus(Long menuId, Integer status) {
        SysMenu menu = getById(menuId);
        if (menu == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "菜单不存在");
        }
        
        menu.setStatus(status);
        setBaseInfo(menu, true);
        
        boolean success = updateById(menu);
        if (success) {
            log.info("更新菜单状态成功，菜单ID：{}，状态：{}", menuId, status);
        }
        return success;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMenuStatusBatch(List<Long> menuIds, Integer status) {
        if (menuIds == null || menuIds.isEmpty()) {
            return true;
        }
        
        for (Long menuId : menuIds) {
            updateMenuStatus(menuId, status);
        }
        
        log.info("批量更新菜单状态成功，菜单数量：{}，状态：{}", menuIds.size(), status);
        return true;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMenuVisible(Long menuId, Integer visible) {
        SysMenu menu = getById(menuId);
        if (menu == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "菜单不存在");
        }
        
        menu.setVisible(visible);
        setBaseInfo(menu, true);
        
        boolean success = updateById(menu);
        if (success) {
            log.info("更新菜单显示状态成功，菜单ID：{}，显示状态：{}", menuId, visible);
        }
        return success;
    }
    
    @Override
    public List<SysMenu> getAllEnabledMenus() {
        return baseMapper.selectAllEnabledMenus();
    }
    
    @Override
    public List<SysMenu> getAllVisibleMenus() {
        return baseMapper.selectAllVisibleMenus();
    }

    @Override
    public List<SysMenu> getAllVisibleNavigationMenus() {
        return baseMapper.selectAllVisibleNavigationMenus();
    }

    @Override
    public List<SysMenu> buildMenuTree() {
        List<SysMenu> allMenus = getAllEnabledMenus();
        return buildTree(allMenus, 0L);
    }

    @Override
    public List<SysMenu> buildUserMenuTree(Long userId) {
        List<SysMenu> userMenus = getMenusByUserId(userId);
        log.info("用户{}的菜单数量: {}", userId, userMenus.size());
        return buildTree(userMenus, 0L);
    }

    @Override
    public int countChildMenus(Long parentId) {
        return baseMapper.countByParentId(parentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMenu(Long menuId) {
        SysMenu menu = getById(menuId);
        if (menu == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "菜单不存在");
        }

        // 检查是否有子菜单
        int childCount = countChildMenus(menuId);
        if (childCount > 0) {
            throw new BusinessException(ResultCode.BAD_REQUEST,
                    String.format("该菜单下还有%d个子菜单，无法删除", childCount));
        }

        // 删除菜单
        boolean success = removeById(menuId);
        if (success) {
            log.info("删除菜单成功，菜单ID：{}，菜单名称：{}", menuId, menu.getMenuName());
        }
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMenusBatch(List<Long> menuIds) {
        if (menuIds == null || menuIds.isEmpty()) {
            return true;
        }

        for (Long menuId : menuIds) {
            deleteMenu(menuId);
        }

        log.info("批量删除菜单成功，菜单数量：{}", menuIds.size());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean moveMenu(Long menuId, Long newParentId) {
        SysMenu menu = getById(menuId);
        if (menu == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "菜单不存在");
        }

        // 检查新父菜单是否存在
        if (newParentId != 0) {
            SysMenu parentMenu = getById(newParentId);
            if (parentMenu == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "父菜单不存在");
            }
        }

        // 检查是否形成循环引用
        if (isCircularReference(menuId, newParentId)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "不能将菜单移动到自己的子菜单下");
        }

        // 检查菜单名称是否冲突
        if (isMenuNameExists(menu.getMenuName(), newParentId, menuId)) {
            throw new BusinessException(ResultCode.CONFLICT, "目标位置已存在同名菜单");
        }

        // 更新父菜单ID
        menu.setParentId(newParentId);
        setBaseInfo(menu, true);

        boolean success = updateById(menu);
        if (success) {
            log.info("移动菜单成功，菜单ID：{}，新父菜单ID：{}", menuId, newParentId);
        }
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean initSystemMenus() {
        log.info("开始初始化系统菜单...");

        List<SysMenu> systemMenus = getSystemMenus();

        // 优化：批量查询现有菜单，避免N+1查询问题
        Set<String> permissionCodes = systemMenus.stream()
                .map(SysMenu::getPermissionCode)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        // 批量查询现有菜单，建立权限编码到菜单的映射
        Map<String, SysMenu> existingMenuMap = new HashMap<>();
        if (!permissionCodes.isEmpty()) {
            List<SysMenu> existingMenus = baseMapper.selectByPermissionCodes(new ArrayList<>(permissionCodes));
            existingMenuMap = existingMenus.stream()
                    .filter(menu -> StrUtil.isNotBlank(menu.getPermissionCode()))
                    .collect(Collectors.toMap(SysMenu::getPermissionCode, menu -> menu));
        }

        // 处理系统菜单
        for (SysMenu menu : systemMenus) {
            SysMenu existingMenu = null;
            if (StrUtil.isNotBlank(menu.getPermissionCode())) {
                existingMenu = existingMenuMap.get(menu.getPermissionCode());
            }

            if (existingMenu == null) {
                // 菜单不存在，创建新菜单
                setBaseInfo(menu, false);
                save(menu);
                log.info("创建系统菜单：{} - {}", menu.getMenuName(), menu.getPermissionCode());
            } else {
                // 菜单已存在，更新菜单信息
                menu.setId(existingMenu.getId());
                setBaseInfo(menu, true);
                updateById(menu);
                log.info("更新系统菜单：{} - {}", menu.getMenuName(), menu.getPermissionCode());
            }
        }

        log.info("系统菜单初始化完成，共处理{}个菜单", systemMenus.size());
        return true;
    }

    /**
     * 构建菜单树（优化版本，使用Map提高性能）
     */
    private List<SysMenu> buildTree(List<SysMenu> menus, Long parentId) {
        if (menus == null || menus.isEmpty()) {
            return new ArrayList<>();
        }

        // 使用Map按parentId分组，避免重复遍历，将时间复杂度从O(n²)降低到O(n)
        Map<Long, List<SysMenu>> menuMap = menus.stream()
                .collect(Collectors.groupingBy(
                        menu -> menu.getParentId() == null ? 0L : menu.getParentId(),
                        LinkedHashMap::new,
                        Collectors.toList()
                ));

        return buildTreeFromMap(menuMap, parentId);
    }

    /**
     * 从Map构建菜单树
     */
    private List<SysMenu> buildTreeFromMap(Map<Long, List<SysMenu>> menuMap, Long parentId) {
        List<SysMenu> children = menuMap.get(parentId);
        if (children == null || children.isEmpty()) {
            return new ArrayList<>();
        }

        // 排序
        children.sort(Comparator.comparing(SysMenu::getSortOrder,
                Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(SysMenu::getCreateTime,
                Comparator.nullsLast(Comparator.naturalOrder())));

        // 递归设置子菜单
        for (SysMenu menu : children) {
            List<SysMenu> subChildren = buildTreeFromMap(menuMap, menu.getId());
            if (!subChildren.isEmpty()) {
                menu.setChildren(subChildren);
            }
        }

        return children;
    }

    /**
     * 检查是否形成循环引用
     */
    private boolean isCircularReference(Long menuId, Long parentId) {
        if (parentId == 0 || Objects.equals(menuId, parentId)) {
            return Objects.equals(menuId, parentId);
        }

        // 获取所有子菜单ID
        List<Long> childIds = baseMapper.selectChildIds(menuId);
        return childIds.contains(parentId);
    }

    /**
     * 获取系统菜单列表
     */
    private List<SysMenu> getSystemMenus() {
        List<SysMenu> menus = new ArrayList<>();

        // 系统管理目录
        menus.add(createMenu(0L, "系统管理", 0, "/system", null, "system", null, 1, 1, "系统管理目录"));

        // 用户管理菜单
        menus.add(createMenu(1L, "用户管理", 1, "/system/user", "system/user/index", "user", "system:user:list", 1, 11, "用户管理菜单"));
        menus.add(createMenu(2L, "新增用户", 2, null, null, null, "system:user:add", 0, 111, "新增用户按钮"));
        menus.add(createMenu(2L, "编辑用户", 2, null, null, null, "system:user:edit", 0, 112, "编辑用户按钮"));
        menus.add(createMenu(2L, "删除用户", 2, null, null, null, "system:user:remove", 0, 113, "删除用户按钮"));

        // 角色管理菜单
        menus.add(createMenu(1L, "角色管理", 1, "/system/role", "system/role/index", "role", "system:role:list", 1, 12, "角色管理菜单"));
        menus.add(createMenu(6L, "新增角色", 2, null, null, null, "system:role:add", 0, 121, "新增角色按钮"));
        menus.add(createMenu(6L, "编辑角色", 2, null, null, null, "system:role:edit", 0, 122, "编辑角色按钮"));
        menus.add(createMenu(6L, "删除角色", 2, null, null, null, "system:role:remove", 0, 123, "删除角色按钮"));
        menus.add(createMenu(6L, "角色授权", 2, null, null, null, "system:role:auth", 0, 124, "角色授权按钮"));

        // 权限管理菜单
        menus.add(createMenu(1L, "权限管理", 1, "/system/permission", "system/permission/index", "permission", "system:permission:list", 1, 13, "权限管理菜单"));
        menus.add(createMenu(11L, "新增权限", 2, null, null, null, "system:permission:add", 0, 131, "新增权限按钮"));
        menus.add(createMenu(11L, "编辑权限", 2, null, null, null, "system:permission:edit", 0, 132, "编辑权限按钮"));
        menus.add(createMenu(11L, "删除权限", 2, null, null, null, "system:permission:remove", 0, 133, "删除权限按钮"));

        return menus;
    }

    /**
     * 创建菜单对象
     */
    private SysMenu createMenu(Long parentId, String menuName, Integer menuType, String path,
                              String component, String icon, String permissionCode,
                               Integer visible, Integer sortOrder, String remark) {
        SysMenu menu = new SysMenu();
        menu.setParentId(parentId);
        menu.setMenuName(menuName);
        menu.setMenuType(menuType);
        menu.setPath(path);
        menu.setComponent(component);
        menu.setIcon(icon);
        menu.setPermissionCode(permissionCode);
        menu.setStatus(1);
        menu.setVisible(visible);
        menu.setSortOrder(sortOrder);
        menu.setRemark(remark);
        return menu;
    }
}
