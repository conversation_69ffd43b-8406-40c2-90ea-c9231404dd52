package com.jcloud.admin.service.impl;

import com.jcloud.admin.service.MenuPermissionService;
import com.jcloud.admin.service.SysRoleMenuService;
import com.jcloud.admin.service.SysUserService;
import com.jcloud.common.entity.SysMenu;
import com.jcloud.common.entity.SysUser;
import com.jcloud.common.manager.PermissionManager;
import com.jcloud.common.mapper.SysMenuMapper;
import com.jcloud.common.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜单权限服务实现类
 * 基于菜单表的统一权限管理
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class MenuPermissionServiceImpl implements MenuPermissionService {

    private final SysMenuMapper menuMapper;
    private final SysRoleMenuService roleMenuService;
    private final SysUserService userService;
    private final PermissionManager permissionManager;

    public MenuPermissionServiceImpl(SysMenuMapper menuMapper,
                                     SysRoleMenuService roleMenuService,
                                     @Lazy SysUserService userService,
                                     PermissionManager permissionManager) {
        this.menuMapper = menuMapper;
        this.roleMenuService = roleMenuService;
        this.userService = userService;
        this.permissionManager = permissionManager;
    }

    @Override
    @Cacheable(value = "userPermissions", key = "#userId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()", unless = "#result == null or #result.isEmpty()")
    public List<String> getUserPermissionCodes(Long userId) {
        Long tenantId = SecurityUtils.getTenantId();
        log.info("🔍 获取用户权限编码: userId={}, tenantId={}", userId, tenantId);

        // 使用统一的权限管理器检查超级管理员
        SysUser user = userService.getById(userId);
        log.info("🔍 用户信息: user={}", user);

        if (permissionManager.isSuperAdmin(user)) {
            log.info("🎯 超级管理员用户，返回通配符权限: userId={}", userId);
            return List.of("*:*:*");
        }

        List<String> permissionCodes = menuMapper.selectPermissionCodesByUserId(userId, tenantId);
        log.info("🔍 普通用户权限编码查询结果: userId={}, permissionCount={}, permissions={}",
                userId, permissionCodes.size(), permissionCodes);

        return permissionCodes;
    }

    @Override
    public boolean hasPermission(Long userId, String permissionCode) {
        if (userId == null || permissionCode == null || permissionCode.trim().isEmpty()) {
            return false;
        }

        // 使用统一的权限管理器进行权限检查
        SysUser user = userService.getById(userId);
        PermissionManager.PermissionCheckResult result = permissionManager.checkPermission(user,
                u -> getUserPermissionCodes(userId).contains(permissionCode));

        if (result.isSuperAdmin()) {
            log.debug("超级管理员用户，拥有所有权限: userId={}, permissionCode={}", userId, permissionCode);
        }
        return result.hasPermission();
    }

    @Override
    public boolean hasAnyPermission(Long userId, String... permissionCodes) {
        if (userId == null || permissionCodes == null || permissionCodes.length == 0) {
            return false;
        }

        // 使用统一的权限管理器进行批量权限检查
        SysUser user = userService.getById(userId);
        List<String> userPermissions = getUserPermissionCodes(userId);
        PermissionManager.PermissionCheckResult result = permissionManager.checkPermissions(
                user, permissionCodes, userPermissions, false);

        if (result.isSuperAdmin()) {
            log.debug("超级管理员用户，拥有所有权限: userId={}", userId);
        }

        return result.hasPermission();
    }

    @Override
    public boolean hasAllPermissions(Long userId, String... permissionCodes) {
        if (userId == null || permissionCodes == null || permissionCodes.length == 0) {
            return false;
        }

        // 使用统一的权限管理器进行批量权限检查
        SysUser user = userService.getById(userId);
        List<String> userPermissions = getUserPermissionCodes(userId);
        PermissionManager.PermissionCheckResult result = permissionManager.checkPermissions(
                user, permissionCodes, userPermissions, true);

        if (result.isSuperAdmin()) {
            log.debug("超级管理员用户，拥有所有权限: userId={}", userId);
        }

        return result.hasPermission();
    }

    @Override
    @Cacheable(value = "rolePermissions", key = "#roleId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public List<String> getRolePermissionCodes(Long roleId) {
        Long tenantId = SecurityUtils.getTenantId();
        log.debug("获取角色权限编码: roleId={}, tenantId={}", roleId, tenantId);

        List<String> permissionCodes = menuMapper.selectPermissionCodesByRoleId(roleId, tenantId);
        log.debug("角色权限编码查询结果: roleId={}, permissionCount={}", roleId, permissionCodes.size());

        return permissionCodes;
    }

    @Override
    public List<SysMenu> getRolePermissionMenus(Long roleId) {
        Long tenantId = SecurityUtils.getTenantId();
        log.debug("获取角色权限菜单: roleId={}, tenantId={}", roleId, tenantId);

        List<SysMenu> permissionMenus = menuMapper.selectPermissionMenusByRoleId(roleId, tenantId);
        log.debug("角色权限菜单查询结果: roleId={}, menuCount={}", roleId, permissionMenus.size());

        return permissionMenus;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "rolePermissions", key = "#roleId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public boolean assignPermissionsToRole(Long roleId, List<String> permissionCodes) {
        Long tenantId = SecurityUtils.getTenantId();
        Long currentUserId = SecurityUtils.getUserId();

        log.info("为角色分配权限: roleId={}, permissionCodes={}", roleId, permissionCodes);

        try {
            // 1. 删除角色现有的权限关联（只删除按钮类型的菜单关联）
            int deletedCount = menuMapper.deleteRolePermissionMenus(roleId, tenantId);
            log.debug("删除角色现有权限关联: roleId={}, deletedCount={}", roleId, deletedCount);

            // 2. 如果权限编码列表不为空，则添加新的关联
            if (permissionCodes != null && !permissionCodes.isEmpty()) {
                // 根据权限编码获取对应的菜单ID
                List<Long> menuIds = menuMapper.selectMenuIdsByPermissionCodes(permissionCodes, tenantId);

                if (!menuIds.isEmpty()) {
                    // 获取角色现有的非权限菜单（目录和页面类型）
                    List<Long> existingNonPermissionMenuIds = menuMapper.selectNonPermissionMenuIdsByRoleId(roleId, tenantId);

                    // 合并现有的非权限菜单和新的权限菜单
                    List<Long> allMenuIds = new ArrayList<>(existingNonPermissionMenuIds);
                    allMenuIds.addAll(menuIds);

                    // 使用现有的角色菜单服务来分配菜单
                    boolean success = roleMenuService.assignMenusToRole(roleId, allMenuIds);
                    if (success) {
                        log.info("角色权限分配成功: roleId={}, assignedPermissionMenuCount={}, totalMenuCount={}",
                                roleId, menuIds.size(), allMenuIds.size());

                        // 清除相关缓存
                        refreshRolePermissionCache(roleId);

                        return true;
                    }
                } else {
                    log.warn("未找到对应的权限菜单: roleId={}, permissionCodes={}", roleId, permissionCodes);
                }
            } else {
                // 如果权限编码列表为空，只保留非权限菜单
                List<Long> existingNonPermissionMenuIds = menuMapper.selectNonPermissionMenuIdsByRoleId(roleId, tenantId);
                boolean success = roleMenuService.assignMenusToRole(roleId, existingNonPermissionMenuIds);
                if (success) {
                    log.info("清空角色权限，保留非权限菜单: roleId={}, nonPermissionMenuCount={}",
                            roleId, existingNonPermissionMenuIds.size());

                    // 清除相关缓存
                    refreshRolePermissionCache(roleId);

                    return true;
                }
            }

            log.info("角色权限分配完成: roleId={}, permissionCount={}", roleId,
                    permissionCodes != null ? permissionCodes.size() : 0);
            return true;

        } catch (Exception e) {
            log.error("角色权限分配失败: roleId={}, permissionCodes={}", roleId, permissionCodes, e);
            throw e;
        }
    }

    @Override
    @Cacheable(value = "allPermissionMenus", key = "T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public List<SysMenu> getAllPermissionMenus() {
        Long tenantId = SecurityUtils.getTenantId();
        log.debug("获取所有权限菜单: tenantId={}", tenantId);

        List<SysMenu> permissionMenus = menuMapper.selectAllPermissionMenus(tenantId);
        log.debug("权限菜单查询结果: tenantId={}, menuCount={}", tenantId, permissionMenus.size());

        return permissionMenus;
    }

    @Override
    public SysMenu getPermissionMenuByCode(String permissionCode) {
        if (permissionCode == null || permissionCode.trim().isEmpty()) {
            return null;
        }

        Long tenantId = SecurityUtils.getTenantId();
        return menuMapper.selectPermissionMenuByCode(permissionCode, tenantId);
    }

    @Override
    public List<SysMenu> getPermissionMenusByParentId(Long parentId) {
        Long tenantId = SecurityUtils.getTenantId();
        return menuMapper.selectPermissionMenusByParentId(parentId, tenantId);
    }

    @Override
    public List<SysMenu> buildPermissionMenuTree() {
        List<SysMenu> allPermissionMenus = getAllPermissionMenus();
        return buildMenuTree(allPermissionMenus, 0L);
    }

    /**
     * 递归构建菜单树（优化版本，使用Map提高性能）
     */
    private List<SysMenu> buildMenuTree(List<SysMenu> allMenus, Long parentId) {
        if (allMenus == null || allMenus.isEmpty()) {
            return new ArrayList<>();
        }

        // 使用Map按parentId分组，避免重复遍历，将时间复杂度从O(n²)降低到O(n)
        Map<Long, List<SysMenu>> menuMap = allMenus.stream()
                .collect(Collectors.groupingBy(
                        menu -> menu.getParentId() == null ? 0L : menu.getParentId(),
                        LinkedHashMap::new,
                        Collectors.toList()
                ));

        return buildTreeFromMap(menuMap, parentId);
    }

    /**
     * 从Map构建菜单树
     */
    private List<SysMenu> buildTreeFromMap(Map<Long, List<SysMenu>> menuMap, Long parentId) {
        List<SysMenu> children = menuMap.get(parentId);
        if (children == null || children.isEmpty()) {
            return new ArrayList<>();
        }

        // 排序
        children.sort(Comparator.comparing(SysMenu::getSortOrder, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(SysMenu::getCreateTime, Comparator.nullsLast(Comparator.naturalOrder())));

        // 递归设置子菜单
        for (SysMenu menu : children) {
            List<SysMenu> subChildren = buildTreeFromMap(menuMap, menu.getId());
            if (!subChildren.isEmpty()) {
                menu.setChildren(subChildren);
            }
        }

        return children;
    }

    @Override
    @CacheEvict(value = "userPermissions", key = "#userId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public void refreshUserPermissionCache(Long userId) {
        log.debug("刷新用户权限缓存: userId={}", userId);
    }

    @Override
    @CacheEvict(value = "rolePermissions", key = "#roleId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()")
    public void refreshRolePermissionCache(Long roleId) {
        log.debug("刷新角色权限缓存: roleId={}", roleId);
    }

    @Override
    @CacheEvict(value = {"userPermissions", "rolePermissions", "allPermissionMenus"}, allEntries = true)
    public void clearAllPermissionCache() {
        log.info("清除所有权限缓存");
    }
}
