package com.jcloud.admin.service;

import com.jcloud.admin.service.impl.SysRoleMenuServiceImpl;
import com.jcloud.common.entity.SysMenu;
import com.jcloud.common.entity.SysRoleMenu;
import com.jcloud.common.mapper.SysMenuMapper;
import com.jcloud.common.mapper.SysRoleMenuMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 角色菜单分配功能测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class RoleMenuAssignmentTest {

    @Autowired
    private SysRoleMenuService roleMenuService;

    @Autowired
    private SysMenuMapper menuMapper;

    @Autowired
    private SysRoleMenuMapper roleMenuMapper;

    /**
     * 测试角色菜单分配功能
     */
    @Test
    public void testRoleMenuAssignment() {
        log.info("开始测试角色菜单分配功能...");

        Long testRoleId = 1L;
        List<Long> testMenuIds = Arrays.asList(1L, 2L, 3L);

        try {
            // 1. 测试分配菜单
            boolean assignResult = roleMenuService.assignMenusToRole(testRoleId, testMenuIds);
            log.info("菜单分配结果: {}", assignResult);

            // 2. 验证分配结果
            List<SysRoleMenu> assignedMenus = roleMenuMapper.selectByRoleId(testRoleId);
            log.info("分配后的菜单数量: {}", assignedMenus.size());

            // 3. 测试清空菜单
            boolean clearResult = roleMenuService.assignMenusToRole(testRoleId, Arrays.asList());
            log.info("清空菜单结果: {}", clearResult);

            // 4. 验证清空结果
            List<SysRoleMenu> clearedMenus = roleMenuMapper.selectByRoleId(testRoleId);
            log.info("清空后的菜单数量: {}", clearedMenus.size());

            log.info("角色菜单分配功能测试完成");

        } catch (Exception e) {
            log.error("角色菜单分配功能测试失败", e);
        }
    }

    /**
     * 测试权限菜单分配功能
     */
    @Test
    public void testPermissionMenuAssignment() {
        log.info("开始测试权限菜单分配功能...");

        Long testRoleId = 1L;
        List<String> testPermissionCodes = Arrays.asList("system:user:list", "system:user:create");

        try {
            // 查询权限对应的菜单
            List<Long> menuIds = menuMapper.selectMenuIdsByPermissionCodes(testPermissionCodes, 1L);
            log.info("权限对应的菜单ID: {}", menuIds);

            if (!menuIds.isEmpty()) {
                // 测试分配权限菜单
                boolean assignResult = roleMenuService.assignMenusToRole(testRoleId, menuIds);
                log.info("权限菜单分配结果: {}", assignResult);

                // 验证分配结果
                List<SysRoleMenu> assignedMenus = roleMenuMapper.selectByRoleId(testRoleId);
                log.info("分配后的菜单数量: {}", assignedMenus.size());
            }

            log.info("权限菜单分配功能测试完成");

        } catch (Exception e) {
            log.error("权限菜单分配功能测试失败", e);
        }
    }

    /**
     * 测试缓存清理功能
     */
    @Test
    public void testCacheClearance() {
        log.info("开始测试缓存清理功能...");

        Long testRoleId = 1L;
        List<Long> testMenuIds = Arrays.asList(1L, 2L);

        try {
            // 分配菜单，这应该会触发缓存清理
            boolean result = roleMenuService.assignMenusToRole(testRoleId, testMenuIds);
            log.info("菜单分配结果（应触发缓存清理）: {}", result);

            // 注意：实际的缓存清理效果需要在集成测试中验证
            log.info("缓存清理功能测试完成（实际效果需要在集成测试中验证）");

        } catch (Exception e) {
            log.error("缓存清理功能测试失败", e);
        }
    }

    /**
     * 测试数据一致性
     */
    @Test
    public void testDataConsistency() {
        log.info("开始测试数据一致性...");

        Long testRoleId = 1L;

        try {
            // 1. 先分配一些菜单
            List<Long> initialMenuIds = Arrays.asList(1L, 2L, 3L);
            roleMenuService.assignMenusToRole(testRoleId, initialMenuIds);

            List<SysRoleMenu> initialMenus = roleMenuMapper.selectByRoleId(testRoleId);
            log.info("初始分配的菜单数量: {}", initialMenus.size());

            // 2. 重新分配不同的菜单
            List<Long> newMenuIds = Arrays.asList(4L, 5L);
            roleMenuService.assignMenusToRole(testRoleId, newMenuIds);

            List<SysRoleMenu> newMenus = roleMenuMapper.selectByRoleId(testRoleId);
            log.info("重新分配后的菜单数量: {}", newMenus.size());

            // 3. 验证数据一致性
            boolean isConsistent = newMenus.size() == newMenuIds.size();
            log.info("数据一致性检查: {}", isConsistent ? "通过" : "失败");

            if (isConsistent) {
                log.info("✅ 数据一致性测试通过");
            } else {
                log.warn("⚠️ 数据一致性测试失败");
            }

        } catch (Exception e) {
            log.error("数据一致性测试失败", e);
        }
    }

    /**
     * 测试边界条件
     */
    @Test
    public void testBoundaryConditions() {
        log.info("开始测试边界条件...");

        Long testRoleId = 1L;

        try {
            // 1. 测试空菜单列表
            boolean emptyResult = roleMenuService.assignMenusToRole(testRoleId, Arrays.asList());
            log.info("空菜单列表分配结果: {}", emptyResult);

            // 2. 测试null菜单列表
            boolean nullResult = roleMenuService.assignMenusToRole(testRoleId, null);
            log.info("null菜单列表分配结果: {}", nullResult);

            // 3. 测试无效角色ID
            try {
                boolean invalidRoleResult = roleMenuService.assignMenusToRole(-1L, Arrays.asList(1L));
                log.info("无效角色ID分配结果: {}", invalidRoleResult);
            } catch (Exception e) {
                log.info("无效角色ID正确抛出异常: {}", e.getMessage());
            }

            log.info("边界条件测试完成");

        } catch (Exception e) {
            log.error("边界条件测试失败", e);
        }
    }
}
