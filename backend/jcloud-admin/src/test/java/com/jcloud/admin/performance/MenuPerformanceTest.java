package com.jcloud.admin.performance;

import com.jcloud.admin.service.SysMenuService;
import com.jcloud.common.entity.SysMenu;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * 菜单性能测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class MenuPerformanceTest {

    @Autowired
    private SysMenuService menuService;

    /**
     * 测试菜单树构建性能
     */
    @Test
    public void testMenuTreeBuildPerformance() {
        log.info("开始菜单树构建性能测试...");

        // 预热
        for (int i = 0; i < 5; i++) {
            menuService.buildMenuTree();
        }

        // 性能测试
        int testCount = 100;
        long totalTime = 0;

        for (int i = 0; i < testCount; i++) {
            long startTime = System.currentTimeMillis();
            List<SysMenu> menuTree = menuService.buildMenuTree();
            long endTime = System.currentTimeMillis();
            
            totalTime += (endTime - startTime);
            
            if (i == 0) {
                log.info("菜单树节点数量: {}", countMenuNodes(menuTree));
            }
        }

        double avgTime = (double) totalTime / testCount;
        log.info("菜单树构建性能测试完成:");
        log.info("- 测试次数: {}", testCount);
        log.info("- 总耗时: {}ms", totalTime);
        log.info("- 平均耗时: {:.2f}ms", avgTime);
        log.info("- 最大可接受耗时: 50ms (建议值)");
        
        if (avgTime > 50) {
            log.warn("⚠️ 菜单树构建性能较差，建议进一步优化");
        } else {
            log.info("✅ 菜单树构建性能良好");
        }
    }

    /**
     * 测试用户菜单树构建性能
     */
    @Test
    public void testUserMenuTreeBuildPerformance() {
        log.info("开始用户菜单树构建性能测试...");

        Long testUserId = 1L; // 使用测试用户ID

        // 预热
        for (int i = 0; i < 5; i++) {
            try {
                menuService.buildUserMenuTree(testUserId);
            } catch (Exception e) {
                log.warn("预热阶段出现异常，可能是测试数据问题: {}", e.getMessage());
                return;
            }
        }

        // 性能测试
        int testCount = 100;
        long totalTime = 0;

        for (int i = 0; i < testCount; i++) {
            long startTime = System.currentTimeMillis();
            try {
                List<SysMenu> userMenuTree = menuService.buildUserMenuTree(testUserId);
                long endTime = System.currentTimeMillis();
                
                totalTime += (endTime - startTime);
                
                if (i == 0) {
                    log.info("用户菜单树节点数量: {}", countMenuNodes(userMenuTree));
                }
            } catch (Exception e) {
                log.warn("用户菜单树构建测试出现异常: {}", e.getMessage());
                return;
            }
        }

        double avgTime = (double) totalTime / testCount;
        log.info("用户菜单树构建性能测试完成:");
        log.info("- 测试次数: {}", testCount);
        log.info("- 总耗时: {}ms", totalTime);
        log.info("- 平均耗时: {:.2f}ms", avgTime);
        log.info("- 最大可接受耗时: 100ms (建议值)");
        
        if (avgTime > 100) {
            log.warn("⚠️ 用户菜单树构建性能较差，建议进一步优化");
        } else {
            log.info("✅ 用户菜单树构建性能良好");
        }
    }

    /**
     * 递归计算菜单节点数量
     */
    private int countMenuNodes(List<SysMenu> menus) {
        if (menus == null || menus.isEmpty()) {
            return 0;
        }

        int count = menus.size();
        for (SysMenu menu : menus) {
            if (menu.getChildren() != null) {
                count += countMenuNodes(menu.getChildren());
            }
        }
        return count;
    }

    /**
     * 测试系统菜单初始化性能
     */
    @Test
    public void testSystemMenuInitPerformance() {
        log.info("开始系统菜单初始化性能测试...");

        long startTime = System.currentTimeMillis();
        try {
            boolean result = menuService.initSystemMenus();
            long endTime = System.currentTimeMillis();
            
            long totalTime = endTime - startTime;
            log.info("系统菜单初始化性能测试完成:");
            log.info("- 初始化结果: {}", result ? "成功" : "失败");
            log.info("- 总耗时: {}ms", totalTime);
            log.info("- 最大可接受耗时: 5000ms (建议值)");
            
            if (totalTime > 5000) {
                log.warn("⚠️ 系统菜单初始化性能较差，建议进一步优化");
            } else {
                log.info("✅ 系统菜单初始化性能良好");
            }
        } catch (Exception e) {
            log.error("系统菜单初始化测试出现异常: {}", e.getMessage(), e);
        }
    }
}
