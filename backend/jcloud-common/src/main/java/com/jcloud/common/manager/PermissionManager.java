package com.jcloud.common.manager;

import com.jcloud.common.entity.SysUser;
import com.jcloud.common.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

/**
 * 统一权限管理器
 * 解决权限验证逻辑分散和超级管理员判断重复的问题
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class PermissionManager {

    /**
     * 检查用户是否为超级管理员
     * 
     * @param user 用户对象
     * @return 是否为超级管理员
     */
    public boolean isSuperAdmin(SysUser user) {
        if (user == null) {
            return false;
        }
        
        boolean isAdmin = user.getIsAdmin() != null && user.getIsAdmin() == 1;
        if (log.isDebugEnabled()) {
            log.debug("检查用户是否为超级管理员: userId={}, isAdmin={}, result={}", 
                    user.getId(), user.getIsAdmin(), isAdmin);
        }
        
        return isAdmin;
    }

    /**
     * 检查用户ID是否为超级管理员
     * 
     * @param userId 用户ID
     * @return 是否为超级管理员
     */
    @Cacheable(value = "userAdminStatus", key = "#userId + ':' + T(com.jcloud.common.util.SecurityUtils).getTenantId()", unless = "#result == null")
    public Boolean isSuperAdmin(Long userId) {
        if (userId == null) {
            return false;
        }
        try {
            // 优先使用SecurityUtils中的方法，它已经有缓存和会话优化
            if (SecurityUtils.getUserId() != null && SecurityUtils.getUserId().equals(userId)) {
                boolean result = SecurityUtils.isSuperAdmin();
                if (log.isDebugEnabled()) {
                    log.debug("通过SecurityUtils检查当前用户超级管理员状态: userId={}, result={}", userId, result);
                }
                return result;
            }
            
            // 对于非当前用户，需要查询数据库（这里需要注入UserService，但为了避免循环依赖，暂时返回false）
            // 实际使用时，调用方应该传入SysUser对象而不是userId
            log.warn("无法检查非当前用户的超级管理员状态，建议传入SysUser对象: userId={}", userId);
            return false;
            
        } catch (Exception e) {
            log.error("检查用户超级管理员状态异常: userId={}, error={}", userId, e.getMessage());
            return false;
        }
    }

    /**
     * 检查当前登录用户是否为超级管理员
     * 
     * @return 是否为超级管理员
     */
    public boolean isCurrentUserSuperAdmin() {
        try {
            return SecurityUtils.isSuperAdmin();
        } catch (Exception e) {
            log.error("检查当前用户超级管理员状态异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 权限验证结果包装类
     */
    public static class PermissionCheckResult {
        private final boolean hasPermission;
        private final boolean isSuperAdmin;
        private final String reason;

        public PermissionCheckResult(boolean hasPermission, boolean isSuperAdmin, String reason) {
            this.hasPermission = hasPermission;
            this.isSuperAdmin = isSuperAdmin;
            this.reason = reason;
        }

        public boolean hasPermission() {
            return hasPermission;
        }

        public boolean isSuperAdmin() {
            return isSuperAdmin;
        }

        public String getReason() {
            return reason;
        }

        public static PermissionCheckResult allow(String reason) {
            return new PermissionCheckResult(true, false, reason);
        }

        public static PermissionCheckResult allowSuperAdmin() {
            return new PermissionCheckResult(true, true, "超级管理员拥有所有权限");
        }

        public static PermissionCheckResult deny(String reason) {
            return new PermissionCheckResult(false, false, reason);
        }
    }

    /**
     * 执行权限检查的通用方法
     * 
     * @param user 用户对象
     * @param permissionChecker 权限检查逻辑
     * @return 权限检查结果
     */
    public PermissionCheckResult checkPermission(SysUser user, PermissionChecker permissionChecker) {
        if (user == null) {
            return PermissionCheckResult.deny("用户不存在");
        }

        // 超级管理员拥有所有权限
        if (isSuperAdmin(user)) {
            if (log.isDebugEnabled()) {
                log.debug("超级管理员用户，拥有所有权限: userId={}", user.getId());
            }
            return PermissionCheckResult.allowSuperAdmin();
        }

        // 执行具体的权限检查逻辑
        try {
            boolean hasPermission = permissionChecker.check(user);
            return hasPermission ? 
                PermissionCheckResult.allow("权限验证通过") : 
                PermissionCheckResult.deny("权限验证失败");
        } catch (Exception e) {
            log.error("权限检查异常: userId={}, error={}", user.getId(), e.getMessage(), e);
            return PermissionCheckResult.deny("权限检查异常: " + e.getMessage());
        }
    }

    /**
     * 权限检查器接口
     */
    @FunctionalInterface
    public interface PermissionChecker {
        boolean check(SysUser user) throws Exception;
    }

    /**
     * 批量权限检查
     * 
     * @param user 用户对象
     * @param permissionCodes 权限编码数组
     * @param userPermissions 用户拥有的权限列表
     * @param checkAll 是否需要拥有所有权限（true）还是任意一个权限（false）
     * @return 权限检查结果
     */
    public PermissionCheckResult checkPermissions(SysUser user, String[] permissionCodes, 
                                                java.util.List<String> userPermissions, boolean checkAll) {
        if (user == null) {
            return PermissionCheckResult.deny("用户不存在");
        }

        if (permissionCodes == null || permissionCodes.length == 0) {
            return PermissionCheckResult.deny("权限编码不能为空");
        }

        // 超级管理员拥有所有权限
        if (isSuperAdmin(user)) {
            return PermissionCheckResult.allowSuperAdmin();
        }

        if (userPermissions == null || userPermissions.isEmpty()) {
            return PermissionCheckResult.deny("用户没有任何权限");
        }

        // 检查权限
        if (checkAll) {
            // 检查是否拥有所有权限
            boolean hasAllPermissions = java.util.Arrays.stream(permissionCodes)
                    .allMatch(userPermissions::contains);
            return hasAllPermissions ? 
                PermissionCheckResult.allow("拥有所有必需权限") : 
                PermissionCheckResult.deny("缺少部分必需权限");
        } else {
            // 检查是否拥有任意一个权限
            boolean hasAnyPermission = java.util.Arrays.stream(permissionCodes)
                    .anyMatch(userPermissions::contains);
            return hasAnyPermission ? 
                PermissionCheckResult.allow("拥有必需权限") : 
                PermissionCheckResult.deny("没有任何必需权限");
        }
    }
}
